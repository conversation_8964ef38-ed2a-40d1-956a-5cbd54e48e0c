import dayjs from "dayjs";

/**
 * Formats date range based on selected time period
 * @param {string} selectedRange - The selected time range (e.g., "Today", "Last Hour", etc.)
 * @returns {Object} - Object containing formattedStart and formattedEnd dates
 */
export const formatDateRange = (selectedRange) => {
  let formattedStart = "";
  let formattedEnd = "";
  const currentDateTime = dayjs();

  if (!selectedRange) {
    return { formattedStart, formattedEnd };
  }

  if (selectedRange.includes("to")) {
    const [startString, endString] = selectedRange.split("to");
    formattedStart = startString;
    formattedEnd = endString;
  } else {
    if (
      selectedRange === "Last Hour" ||
      selectedRange === "Last 6 Hours" ||
      selectedRange === "Last 12 Hours" ||
      selectedRange === "Last 24 Hours"
    ) {
      const hours = {
        "Last Hour": 1,
        "Last 6 Hours": 6,
        "Last 12 Hours": 12,
        "Last 24 Hours": 24,
      };

      const lastXHours = currentDateTime.subtract(hours[selectedRange], "hour");
      if (selectedRange === "Last Hour") {
        formattedStart = lastXHours.format("YYYY-MM-DD HH:mm:ss");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
      } else {
        formattedStart = lastXHours.format("YYYY-MM-DD HH:00:00");
        formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
      }
    } else if (selectedRange === "Today") {
      formattedStart = currentDateTime
        .startOf("day")
        .format("YYYY-MM-DD HH:mm:ss");
      formattedEnd = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
    } else if (selectedRange === "Yesterday") {
      const yesterday = currentDateTime.subtract(1, "day");
      formattedStart = yesterday
        .startOf("day")
        .format("YYYY-MM-DD HH:mm:ss");
      formattedEnd = yesterday.endOf("day").format("YYYY-MM-DD HH:mm:ss");
    } else if (selectedRange === "Last Seven Days") {
      formattedStart = currentDateTime
        .subtract(6, "days")
        .startOf("day")
        .format("YYYY-MM-DD HH:00:00");
      formattedEnd = currentDateTime.format("YYYY-MM-DD HH:00:00");
    } else if (selectedRange === "Last Week") {
      formattedStart = currentDateTime
        .subtract(1, "week")
        .startOf("week")
        .format("YYYY-MM-DD HH:00:00");
      formattedEnd = currentDateTime
        .subtract(1, "week")
        .endOf("week")
        .format("YYYY-MM-DD HH:00:00");
    } else if (selectedRange === "Last 30 Days") {
      formattedStart = currentDateTime
        .subtract(29, "days")
        .startOf("day")
        .format("YYYY-MM-DD HH:00:00");
      formattedEnd = currentDateTime
        .endOf("day")
        .format("YYYY-MM-DD HH:00:00");
    } else if (selectedRange === "Last Month") {
      formattedStart = currentDateTime
        .subtract(1, "month")
        .startOf("month")
        .format("YYYY-MM-DD HH:00:00");
      formattedEnd = currentDateTime
        .subtract(1, "month")
        .endOf("month")
        .format("YYYY-MM-DD HH:00:00");
    } else if (selectedRange === "This Month") {
      formattedStart = currentDateTime
        .startOf("month")
        .format("YYYY-MM-DD HH:mm:ss");
      formattedEnd = currentDateTime
        .endOf("day")
        .format("YYYY-MM-DD HH:mm:ss");
    }
  }

  return { formattedStart, formattedEnd };
};

/**
 * Formats date for display purposes
 * @param {string} value - Date value to format
 * @returns {string} - Formatted date string
 */
export const formatDisplayDate = (value) => {
  return dayjs(value, { format: "YYYY-MM-DDTHH:mm:ss" }).isValid()
    ? dayjs(value).format("DD/MM/YYYY HH:mm:ss")
    : value;
};
