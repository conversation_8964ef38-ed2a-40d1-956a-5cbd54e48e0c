import { formatDisplayDate } from "./dateFormatting";

/**
 * Generates dynamic columns for table based on response data
 * @param {Object} responseData - Response data from API
 * @param {string} visualizationType - Type of visualization
 * @returns {Array} - Array of column definitions
 */
export const generateTableColumns = (responseData, visualizationType) => {
  if (visualizationType !== "Table Report") {
    return [];
  }

  if (!responseData?.data || responseData?.data?.length === 0) {
    return [];
  }

  const firstItem = responseData?.data[0];
  if (!firstItem) {
    return [];
  }

  const keys = Object.keys(firstItem);

  const dynamicColumns = keys.map((key) => ({
    header: key,
    accessorKey: key,
    Cell: ({ row }) => {
      const value = row.original[key];
      if (key === "Date") {
        return formatDisplayDate(value);
      }
      return value;
    },
  }));

  return dynamicColumns;
};

/**
 * Calculates pagination info for table
 * @param {Object} params - Pagination parameters
 * @returns {Object} - Pagination information
 */
export const calculatePaginationInfo = ({
  currentPage,
  limitPerPage,
  totalCount,
}) => {
  const startRecord = (currentPage - 1) * limitPerPage + 1;
  const endRecord = Math.min(limitPerPage * currentPage, totalCount);
  
  return {
    startRecord,
    endRecord,
    totalCount,
    displayText: `${startRecord} - ${endRecord} of ${totalCount} rows`,
  };
};
