import React from "react";
import { CircularProgress } from "@mui/material";
import { InfoIcon } from "../../icons";
import { CssTooltip } from "../StyledComponent";
import { SelectedFiltersDisplay } from "../../common/selectedFiltersDisplay";
import bgImage from "../../assets/img/Records.png";
import ChartRenderers from "./ChartRenderers";

const MainContent = ({
  loadingData,
  responseData,
  panelById,
  date,
  errorMessage,
  colors,
  columns,
  refs,
  resultPerPage,
  limitPerPage,
  handleLimitChange,
  currentPage,
  handlePageChange,
}) => {
  if (loadingData) {
    return (
      <div className="mt-3 bg-white p-5 w-[65vw]">
        <div className="my-5 flex justify-center items-center">
          <CircularProgress size={40} />
        </div>
      </div>
    );
  }

  if (!responseData || !panelById) {
    return null;
  }

  return (
    <>
      {/* Date Range and Filter Info */}
      <div className="font-semibold flex-grow flex items-center justify-between mb-3 m-5">
        <div className="text-sm">
          {"From :"} {date?.startDate || ""}
          {" - "}
          {"To :"} {date?.endDate || ""}
        </div>
        <div className="flex font-hebrew text-sm whitespace-nowrap">
          <div className="flex">
            Selected Filters:
            <CssTooltip
              title={<SelectedFiltersDisplay conditions={panelById?.filters} />}
              placement="bottom"
              arrow
            >
              <InfoIcon className="ml-2 w-4 mt-1 h-3.5" />
            </CssTooltip>
          </div>
        </div>
      </div>

      {/* Content Area */}
      {errorMessage ? (
        <div className="border border-outerBorder mb-5 mt-8">
          <div className="flex text-headingColor text-2xl justify-center font-bold mt-5">
            Oops! No records to display.
          </div>
          <div className="flex justify-center my-10">
            <img
              src={bgImage}
              className="h-[10%] w-[10%] object-cover"
              alt="bg"
            />
          </div>
        </div>
      ) : (
        <ChartRenderers
          panelById={panelById}
          responseData={responseData}
          colors={colors}
          columns={columns}
          refs={refs}
          resultPerPage={resultPerPage}
          limitPerPage={limitPerPage}
          handleLimitChange={handleLimitChange}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
        />
      )}
    </>
  );
};

export default MainContent;
