/**
 * Handles PDF download for chart visualizations
 * @param {Object} params - PDF download parameters
 */
export const handleChartPDFDownload = async ({
  visualizationType,
  targetElement,
  data,
}) => {
  if (!targetElement) {
    console.warn("Target element not found for PDF generation");
    return;
  }

  try {
    // Dynamic imports for better code splitting
    const [
      { usePDF },
      { default: html2canvas },
      { jsPDF }
    ] = await Promise.all([
      import("react-to-pdf"),
      import("html2canvas"),
      import("jspdf")
    ]);

    const filename = `${data}_${visualizationType?.replace(/\s+/g, "")}_${
      new Date().toISOString().split("T")[0]
    }.pdf`;

    const canvas = await html2canvas(targetElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
    });

    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF("l", "mm", "a4");
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const imgX = (pdfWidth - imgWidth * ratio) / 2;
    const imgY = 30;

    pdf.addImage(
      imgData,
      "PNG",
      imgX,
      imgY,
      imgWidth * ratio,
      imgHeight * ratio
    );
    pdf.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
  }
};

/**
 * Gets the target element for PDF generation based on visualization type
 * @param {string} visualizationType - Type of visualization
 * @param {Object} refs - Object containing chart refs
 * @returns {HTMLElement|null} - Target element for PDF generation
 */
export const getTargetElementForPDF = (visualizationType, refs) => {
  const { pieChartRef, barChartRef, lineChartRef, multiAxisRef } = refs;

  switch (visualizationType) {
    case "Pie Chart":
      return pieChartRef.current;
    case "Bar Graph":
      return barChartRef.current;
    case "Line Graph":
      return lineChartRef.current;
    case "MultiAxis Graph":
      return multiAxisRef.current;
    default:
      console.warn("Unknown visualization type:", visualizationType);
      return null;
  }
};
