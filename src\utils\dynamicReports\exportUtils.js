import React from "react";
import dayjs from "dayjs";
import { downloadPanel } from "../../services/panels-api";
import { formatDate } from "../fileDateFormator";
import { formatDateRange } from "./dateFormatting";

/**
 * Calculates export permissions based on total count and configuration
 * @param {number} totalCount - Total number of records
 * @param {Object} configApiData - Configuration data from API
 * @returns {Object} - Export permissions for different file types
 */
export const calculateExportPermissions = (totalCount, configApiData) => {
  if (!configApiData) return { csv: true, pdf: true, excel: true };

  const {
    MAX_ROWS_FOR_CSV,
    MAX_ROWS_FOR_PDF,
    MAX_ROWS_FOR_EXCEL,
    INITIATE_OFFLINE_DOWNLOAD,
  } = configApiData;

  const isOfflineDownload = totalCount >= INITIATE_OFFLINE_DOWNLOAD;

  return {
    csv: isOfflineDownload || totalCount <= MAX_ROWS_FOR_CSV,
    pdf: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_PDF,
    excel: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_EXCEL,
  };
};

/**
 * Generates tooltip content for download button
 * @param {Object} configApiData - Configuration data from API
 * @returns {JSX.Element} - Tooltip content component
 */
export const generateTooltipContent = (configApiData) => (
  <div className="text-sm">
    <div className="font-bold text-xs mb-1">
      Configured number of rows for download:
    </div>
    <div className="font-semibold text-xs mb-1">
      CSV:{" "}
      {configApiData?.MAX_ROWS_FOR_CSV?.toLocaleString("en-IN") ||
        "Loading..."}
    </div>
    <div className="font-semibold text-xs mb-1">
      EXCEL:{" "}
      {configApiData?.MAX_ROWS_FOR_EXCEL?.toLocaleString("en-IN") ||
        "Loading..."}
    </div>
    <div className="font-semibold text-xs mb-1">
      PDF:{" "}
      {configApiData?.MAX_ROWS_FOR_PDF?.toLocaleString("en-IN") ||
        "Loading..."}
    </div>
    <div className="font-semibold text-xs mb-1">
      Offline (Only in CSV):{" "}
      {configApiData?.INITIATE_OFFLINE_DOWNLOAD?.toLocaleString("en-IN") ||
        "Loading..."}
    </div>
  </div>
);

/**
 * Builds request data for export
 * @param {Object} params - Parameters for building request data
 * @returns {Object} - Request data object
 */
export const buildExportRequestData = ({
  panelById,
  filters,
  timeZone,
  limitPerPage,
  currentPage,
  type,
}) => {
  const { formattedStart, formattedEnd } = formatDateRange(panelById.timePeriod);

  let reqData = {
    reportName: "Dynamic Report",
    name: panelById.name,
    visualizationType: panelById.visualizationType,
    filters: [],
    dataColumns: {
      derivedFields: panelById.dataColumns
        ? panelById.dataColumns.derivedFields
        : [],
      tableFields: panelById?.dataColumns?.tableFields,
    },
    startDate: formattedStart,
    endDate: formattedEnd,
    timezone: timeZone,
    reportFilters: filters,
    download: 1,
    type: type,
    limit: limitPerPage,
    page: currentPage,
  };

  // Add panel filters
  panelById.filters.forEach((condition) => {
    reqData.filters.push({
      field: condition.field,
      condition: condition.condition,
      value: condition.value,
      operator: condition.operator,
    });
  });

  return reqData;
};

/**
 * Generates filename for export
 * @param {Object} params - Parameters for filename generation
 * @returns {string} - Generated filename
 */
export const generateExportFilename = ({ data, date, filters, type }) => {
  const extensionType =
    type === "CSV" ? ".csv" : type === "EXCEL" ? ".xlsx" : ".pdf";
  const startDate =
    date?.startDate?.split(" ")[0] || dayjs().format("YYYY-MM-DD");
  const endDate = date?.endDate?.split(" ")[0] || dayjs().format("YYYY-MM-DD");
  const currentTime = formatDate();
  
  return `${data} ${startDate} - ${endDate} ${currentTime}${
    Object.keys(filters).length > 0 ? "_filters" : ""
  }${extensionType}`;
};

/**
 * Handles the export process
 * @param {Object} params - Export parameters
 * @returns {Promise} - Export promise
 */
export const handleExportProcess = async ({
  panelById,
  filters,
  timeZone,
  limitPerPage,
  currentPage,
  type,
  data,
  date,
  responseData,
  configApiData,
  setIsDownloading,
}) => {
  if (!panelById || type === "") return;

  const filename = generateExportFilename({ data, date, filters, type });

  if (
    responseData?.count < (configApiData?.INITIATE_OFFLINE_DOWNLOAD || 50000)
  ) {
    setIsDownloading(true);
  }

  const reqData = buildExportRequestData({
    panelById,
    filters,
    timeZone,
    limitPerPage,
    currentPage,
    type,
  });

  try {
    const response = await downloadPanel({ reqData });
    
    if (
      responseData?.count >
      (configApiData?.INITIATE_OFFLINE_DOWNLOAD || 50000)
    ) {
      console.log("Offline download initiated");
    } else {
      const url = URL.createObjectURL(response.data);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      link.click();
      URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error("Error downloading table report:", error);
  } finally {
    setIsDownloading(false);
  }
};
