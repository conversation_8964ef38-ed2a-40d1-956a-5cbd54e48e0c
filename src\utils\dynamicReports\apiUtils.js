import { formatDateRange } from "./dateFormatting";

/**
 * Builds request data for preview API call
 * @param {Object} params - Parameters for building request data
 * @returns {Object} - Request data object for API call
 */
export const buildPreviewRequestData = ({
  data,
  reportFilters = {},
  isDownload = false,
  timeZone,
  limitPerPage,
  currentPage,
}) => {
  const { formattedStart, formattedEnd } = formatDateRange(data.timePeriod);

  let reqData = {
    reportName: "Dynamic Report",
    name: data.name,
    visualizationType: data.visualizationType,
    filters: [],
    dataColumns: {
      derivedFields: data.dataColumns ? data.dataColumns.derivedFields : [],
    },
    startDate: formattedStart,
    endDate: formattedEnd,
    timezone: timeZone,
    reportFilters: reportFilters,
  };

  // Add download parameter for Table Reports when downloading
  if (isDownload && data.visualizationType === "Table Report") {
    reqData.download = 1;
  }

  // Configure data columns based on visualization type
  if (data.visualizationType === "Bar Graph") {
    reqData.dataColumns["X-Axis"] = data.dataColumns["X-Axis"];
    reqData.dataColumns.noOfRecords = parseInt(data.dataColumns.noOfRecords);
  } else {
    reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
  }

  // Add interval for time-based charts
  if (
    data.visualizationType === "Line Graph" ||
    data.visualizationType === "MultiAxis Graph"
  ) {
    reqData.interval = data.interval;
  }

  // Add pagination for table reports
  if (data.visualizationType === "Table Report") {
    reqData.limit = limitPerPage;
    reqData.page = currentPage;
  }

  // Add panel filters
  data.filters.forEach((condition) => {
    reqData.filters.push({
      field: condition.field,
      condition: condition.condition,
      value: condition.value,
      operator: condition.operator,
    });
  });

  return reqData;
};

/**
 * Handles API success response
 * @param {Object} params - Success handler parameters
 */
export const handleAPISuccess = ({
  data,
  setErrorMessage,
  setResponseData,
  setDate,
  formattedStart,
  formattedEnd,
}) => {
  setErrorMessage(false);
  setResponseData(data);
  setDate({ startDate: formattedStart, endDate: formattedEnd });
};

/**
 * Handles API error response
 * @param {Object} params - Error handler parameters
 */
export const handleAPIError = ({ error, setErrorMessage }) => {
  setErrorMessage(true);
  console.error("API Error:", error);
};
