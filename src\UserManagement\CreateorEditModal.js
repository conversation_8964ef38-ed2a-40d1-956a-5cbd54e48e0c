import { useState, useRef, useEffect } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import Dialog from "@mui/material/Dialog";
import { reportService } from "../services/staticreport.service";
import { emailValidation, nameValidation } from "../common/yupValidation";
import {
  countryCodeArr,
  dialCodeToCountryCodeMap,
} from "../common/countryCode";
import { PersonAdd, CloseIcon } from "../icons";
import { userService } from "../services/user.service";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { isValidPhoneNumber } from "libphonenumber-js";
import { getAll } from "../services/dashboard-api";
import { useQuery } from "react-query";
import { roleService } from "../services/roles.service";
import BackButton from "../components/Button/Button";
import Button from "../components/Button/OutlinedButton";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import CustomDropDown from "../components/Dropdown/CustomeDropdown";
import { RadioGroup, Radio } from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormHelperText from "@mui/material/FormHelperText";
import theme from "../tailwind-theme";
import ErrorDialog from "../popups/ErrorDialog";

function CreateorEditModal({
  show,
  onHide,
  user,
  onConfirm,
  handleSetMessage,
  handleSuccessDialog,
  setImgErr,
  imgErr,
  editClicked,
  setEditClicked,
}) {
  const [file, setFile] = useState(null);
  const [rolesState, setRolesState] = useState([]);
  const [dashboard, setDashboard] = useState([]);
  const [selectObjectsCustomer, setSelectObjectsCustomer] = useState([]);
  const [selectObjectsSupplier, setSelectObjectsSupplier] = useState([]);
  const [isPredefined, setIsPredefined] = useState(false);
  const [predefinedType, setPredefinedType] = useState("");
  const [profileData, setProfileData] = useState(null);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");

  const fileInputRef = useRef(null);

  useEffect(() => {
    if (user && user.profileImage) {
      setProfileData(user.profileImage);
      setEditClicked(false);
    }
    if (editClicked && user?.profileImage) {
      setSelectedImage(user.profileImage);
    }
  }, [user, editClicked, setEditClicked]);

  useQuery(["dashboardList"], getAll, {
    onSuccess: ({ data }) => {
      setDashboard(data?.data);
    },
  });
  useQuery(["RolesList"], () => roleService.getRoleDetails(), {
    onSuccess: ({ data }) => {
      setRolesState(data?.data);
    },
  });
  useQuery(["CustomerList"], () => reportService.getCustomers(), {
    onSuccess: ({ data }) => {
      const cust = [
        {
          value: "Select All",
          label: "Select All",
        },
        ...data
          .map((element) => ({
            value: element.id,
            label: element.name,
            customer_bind: element.customer_bind,
          }))
          .filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.label === item.label)
          ),
      ];

      setSelectObjectsCustomer(cust);
    },
  });

  useQuery(["SupplierList"], () => reportService.getSuppliers(), {
    onSuccess: ({ data }) => {
      const supplier = [
        {
          value: "Select All",
          label: "Select All",
        },
        ...data
          .map((element) => ({
            value: element.id,
            label: element.name,
            supplier_bind: element.supplier_bind,
          }))
          .filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.label === item.label)
          ),
      ];

      setSelectObjectsSupplier(supplier);
    },
  });

  const customerData =
    user && user?.allCustomerList
      ? ["Select All"]
      : Array.isArray(user?.customerList)
      ? user.customerList
          .filter((id) => id !== "Select All")
          .map(
            (id) =>
              selectObjectsCustomer.find((customer) => customer.value === id)
                ?.value || ""
          )
      : [];

  const selectValues = selectObjectsCustomer.map((obj) => obj.value);

  const missingValues = selectValues.filter(
    (value) => !customerData.includes(value)
  );
  if (missingValues.length === 1 && missingValues[0] === "Select All") {
    customerData.push("Select All");
  }
  //console.log("customerData", customerData);

  const supplierData =
    user && user?.allSupplierList
      ? ["Select All"]
      : Array.isArray(user?.supplierList)
      ? user.supplierList
          .filter((id) => id !== "Select All")
          .map(
            (id) =>
              selectObjectsSupplier.find((supplier) => supplier.value === id)
                ?.value || ""
          )
      : [];

  const selectValues1 = selectObjectsSupplier.map((obj) => obj.value);
  const missingValues1 = selectValues1.filter(
    (value) => !customerData.includes(value)
  );
  if (missingValues1.length === 1 && missingValues1[0] === "Select All") {
    customerData.push("Select All");
  }
  const initialValues = {
    // name: user ? (user?.isLdapUser === true ? "" : user.name) : "",
    name: user ? user.name : "",
    email: user ? user.email : "",
    role: user ? user.roleId : "",
    phoneNumber: user ? user.phoneNumber : "",
    countryCode: user ? user?.countryCode : "+91",
    password: "",
    confirmPassword: "",
    image: "",
    defaultDashboard:
      user && dashboard?.length > 0 ? user?.defaultDashboard : "",
    typeList: user ? user.type : "",
    customerList: customerData,
    supplierList: supplierData,
    isLdapUser: user ? (user?.isLdapUser ? "true" : "false") : "true",
  };
  function handleMobileNumber(e) {
    try {
      const countryCode = inferCountryCodeFromDialCode(e.countryCode);
      if (!countryCode) {
        return false;
      }
      const isValid = isValidPhoneNumber(
        String(e.phoneNumber),
        String(countryCode)
      );
      return isValid;
    } catch (error) {
      return false;
    }
  }

  function inferCountryCodeFromDialCode(dialCode) {
    return dialCodeToCountryCodeMap[dialCode];
  }

  const [selectedImage, setSelectedImage] = useState(null);

  const handleImageChange = (event) => {
    const file = event.currentTarget.files[0];
    if (!file || file.length === 0) {
      return false;
    }

    const acceptedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/bmp",
      "image/gif",
    ];

    if (!acceptedTypes.includes(file.type)) {
      setImgErr("Please upload allowed file type");
      event.currentTarget.value = null;
      setFile(null);
      setSelectedImage(null);
      return false;
    }

    if (file) {
      const maxSizeInBytes = 3 * 1024 * 1024; // 3MB
      if (file.size > maxSizeInBytes) {
        setImgErr("File size more than 3MB cannot be uploaded");
        event.currentTarget.value = null;
        setFile(null);
        setSelectedImage(null);
        return false;
      }
      setImgErr("");
      setFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setSelectedImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleValidateCall = (values) => {
    if (hasKeyForId(values.role, "customerRole")) {
      setPredefinedType("Customer");
      setIsPredefined(true);
    } else if (hasKeyForId(values.role, "supplierRole")) {
      setIsPredefined(true);
      setPredefinedType("Supplier");
    } else if (hasKeyForId(values.role, "customerSupplierRole")) {
      setIsPredefined(true);
      setPredefinedType("Both");
    } else {
      setPredefinedType("");
      setIsPredefined(false);
    }
  };

  function hasKeyForId(id, key) {
    const obj = rolesState.find((item) => item.id === id);
    if (obj) {
      if (obj.hasOwnProperty(key) && obj[key] === true) {
        return true;
      }
    }
    return false;
  }

  const customerSupplierRoleCheck = (id, keys) => {
    const obj = rolesState.find((item) => item.id === id);

    if (obj) {
      for (const key of keys) {
        if (obj.hasOwnProperty(key) && obj[key] === true) {
          return true;
        }
      }
    }
    return false;
  };

  const removeImage = () => {
    setSelectedImage(null);
    setFile(null);
    setImgErr("");
    setProfileData(null);
  };

  return (
    <div>
      <div className="mx-5">
        <Formik
          initialValues={initialValues}
          enableReinitialize={true}
          validateOnBlur={true}
          validate={handleValidateCall}
          validationSchema={Yup.object({
            name: nameValidation.required("User name is Required"),
            email: emailValidation,
            typeList: Yup.string().when("role", {
              is: (roleId, schema) => {
                // Backend flags to check
                const keysToCheck = [
                  "customerSupplierRole",
                  "supplierRole",
                  "customerRole",
                ];

                return !customerSupplierRoleCheck(roleId, keysToCheck);
              },

              then: (schema) => schema.required("Type is required"),
              otherwise: (schema) => schema.optional(),
            }),
            defaultDashboard: Yup.string()
              .nullable()
              .notRequired()
              .when("role", {
                is: (roleId) => {
                  const keysToCheck = [
                    "customerSupplierRole",
                    "supplierRole",
                    "customerRole",
                  ];
                  return !customerSupplierRoleCheck(roleId, keysToCheck);
                },
                then: (schema) => schema.required("Dashboard is required"),
                otherwise: (schema) => schema.optional(),
              }),
            phoneNumber: Yup.string()
              .test(
                "phone-validation",
                "Invalid phone number",
                function (value) {
                  const { countryCode } = this.parent;
                  const sol = handleMobileNumber({
                    phoneNumber: value,
                    countryCode,
                  });
                  return sol;
                }
              )
              .required("Phone number is required"),
            role: Yup.string().required("Role is Required"),
            image: Yup.mixed().test(
              "fileFormat",
              "Unsupported file format of image uploaded",
              function (value) {
                if (value) {
                  return [
                    "image/jpeg",
                    "image/png",
                    "image/bmp",
                    "image/gif",
                  ].includes(value.type);
                }
                return true;
              }
            ),
            customerList: Yup.array().test(
              "customerList",
              "Customer List is required",
              function (value) {
                const { typeList } = this.parent;

                if (
                  typeList === "Customer" ||
                  typeList === "Both" ||
                  predefinedType === "Customer" ||
                  predefinedType === "Both"
                ) {
                  return value && value.length > 0;
                }
                return true;
              }
            ),
            supplierList: Yup.array().test(
              "supplierList",
              "Supplier List is required",
              function (value) {
                const { typeList } = this.parent;
                if (
                  typeList === "Supplier" ||
                  typeList === "Both" ||
                  predefinedType === "Supplier" ||
                  predefinedType === "Both"
                ) {
                  return value && value.length > 0;
                }
                return true;
              }
            ),
          })}
          onSubmit={(
            {
              email,
              password,
              name,
              image,
              role,
              phoneNumber,
              customerList,
              supplierList,
              defaultDashboard,
              countryCode,
              values,
              typeList,
              isLdapUser,
            },
            { setStatus, setSubmitting, resetForm }
          ) => {
            let allCustomerList = false;
            let allSupplierList = false;

            if (customerList.includes("Select All")) {
              allCustomerList = true;
              customerList = [];
            }

            if (supplierList.includes("Select All")) {
              allSupplierList = true;
              supplierList = [];
            }

            const selectedCustomers = selectObjectsCustomer
              .filter((customer) => customerList.includes(customer.value))
              .map(({ label, value, customer_bind }) => ({
                name: label,
                id: value,
                customer_bind: customer_bind,
              }));

            const selectedSuppliers = selectObjectsSupplier
              .filter((supplier) => supplierList.includes(supplier.value))
              .map(({ label, value, supplier_bind }) => ({
                name: label,
                id: value,
                supplier_bind: supplier_bind,
              }));

            const customerListData = allCustomerList ? [] : selectedCustomers;
            const supplierListData = allSupplierList ? [] : selectedSuppliers;

            setSubmitting(true);
            const formData = new FormData();
            formData.append("image", file);
            formData.append("name", name);
            formData.append("role", role);
            formData.append("phoneNumber", phoneNumber);
            formData.append("countryCode", countryCode);
            formData.append(
              "defaultDashboard",
              isPredefined ? "" : defaultDashboard
            );
            formData.append("email", email);
            formData.append("isLdapUser", isLdapUser);
            formData.append("isPredefined", isPredefined ? true : false);

            formData.append(
              "type",
              predefinedType === "" ? typeList : predefinedType
            );

            if (typeList === "Customer" || predefinedType === "Customer") {
              formData.append("customerList", JSON.stringify(customerListData));
              formData.append("supplierList", JSON.stringify([]));
              formData.append("allCustomerList", allCustomerList);
              formData.append("allSupplierList", false);
            } else if (
              typeList === "Supplier" ||
              predefinedType === "Supplier"
            ) {
              formData.append("supplierList", JSON.stringify(supplierListData));
              formData.append("customerList", JSON.stringify([]));
              formData.append("allSupplierList", allSupplierList);
              formData.append("allCustomerList", false);
            } else if (typeList === "Both" || predefinedType === "Both") {
              formData.append("customerList", JSON.stringify(customerListData));
              formData.append("supplierList", JSON.stringify(supplierListData));
              formData.append("allCustomerList", allCustomerList);
              formData.append("allSupplierList", allSupplierList);
            }

            let promise;
            if (!user) {
              // formData.append("password", password);
              promise = userService.create(formData);
            } else {
              promise = userService.update(user.id, formData);
            }
            let errorOccurred = false;
            promise
              .then((res) => {
                handleSuccessDialog();
                resetForm();
                setFile(null);
                if (!user) {
                  handleSetMessage("User is successfully created ");
                } else {
                  handleSetMessage("User Updated Successfully");
                }
                setSelectedImage(null);
                if (res?.data?.id) {
                  onConfirm();
                }
              })
              .catch((error) => {
                // errorOccurred = true;
                setErrorDialog(true);
                setMessage(
                  error?.response?.data?.message
                    ? error?.response?.data?.message
                    : error
                );
                setSubmitting(false);
                // setSelectedImage(null);
              })

              .finally(() => {
                if (errorOccurred) {
                  setTimeout(() => {
                    setSubmitting(false);
                  }, 3000);
                } else {
                  setSubmitting(false);
                }
              });
          }}
        >
          {({
            errors,
            status,
            touched,
            isSubmitting,
            isValid,
            setFieldValue,
            resetForm,
            values,
            handleBlur,
            handleChange,
            setFieldTouched,
          }) => (
            <Dialog
              open={show}
              onClose={() => {
                onHide();
                resetForm();
                setFile(null);
                setSelectedImage(null);
                setProfileData(null);
              }}
              sx={{
                "& .MuiDialog-container": {
                  "& .MuiPaper-root": {
                    width: "100%",
                    maxWidth: "450px", // Set your width here
                    //height: "350px",
                    margin: 0,
                  },
                },
              }}
            >
              <Form>
                <div className="mx-5">
                  <div className=" mt-4 flex items-center justify-between ">
                    <div className="flex flex-row text-tabColor font-semibold">
                      <PersonAdd className="mr-2" />
                      <span>{!user ? "Add User" : "Edit User"}</span>
                    </div>
                    <CloseIcon
                      onClick={() => {
                        onHide();
                        resetForm();
                        setFile(null);
                        setSelectedImage(null);
                        setProfileData(null);
                      }}
                      className="w-3 h-3 cursor-pointer"
                    ></CloseIcon>
                  </div>

                  <div className="mt-2 border-b border-panelBorder" />
                  <div className="flex flex-row mt-5">
                    {profileData || selectedImage ? (
                      <div
                        style={{
                          width: "16%",
                        }}
                      >
                        <img
                          src={
                            selectedImage !== null ? selectedImage : profileData
                          }
                          style={{
                            borderRadius: "25px",
                            width: "50px",
                            height: "50px",
                          }}
                          alt="Profile"
                        />
                      </div>
                    ) : (
                      <div
                        style={{
                          width: "50px",
                          height: "50px",
                          borderRadius: "25px",
                          backgroundColor:
                            selectedImage !== null
                              ? selectedImage
                              : "lightgrey",
                        }}
                      ></div>
                    )}
                    <div
                      style={{
                        width: "75%",
                        padding: "12px",
                        position: "relative",
                      }}
                    >
                      <input
                        ref={fileInputRef}
                        style={{
                          display: "none",
                        }}
                        type="file"
                        name="image"
                        accept=".jpg, .jpeg, .png, .bmp ,.gif"
                        onChange={handleImageChange}
                      />
                      <div className="flex gap-2">
                        <div
                          style={{
                            border: `1px solid ${theme.borderColor.errorBorder}`,
                            color: theme.borderColor.errorBorder,
                          }}
                          className="cursor-pointer rounded-[3px] w-14 h-6 text-xs items-center"
                          onClick={handleButtonClick}
                        >
                          <span className="flex justify-center items-center mt-0.5 font-semibold">
                            {profileData !== null || selectedImage
                              ? "Edit"
                              : "Upload"}
                          </span>
                        </div>
                        {profileData !== null || selectedImage ? (
                          <div
                            style={{
                              border: `1px solid ${theme.borderColor.errorBorder}`,
                              color: theme.borderColor.errorBorder,
                            }}
                            className="cursor-pointer rounded-[3px] w-14 h-6 text-xs items-center"
                            onClick={() => {
                              removeImage();
                            }}
                          >
                            <span className="flex justify-center items-center mt-0.5 font-semibold">
                              {"Remove"}
                            </span>
                          </div>
                        ) : null}
                      </div>

                      <p
                        style={{
                          color: theme.textColor.tabColor,
                          fontSize: "10px",
                          fontWeight: "300",
                          fontStyle: "italic",
                          paddingTop: "2px",
                        }}
                      >
                        Jpeg/png/bmp/gif only . 3MB max
                      </p>
                      <p className="text-errorColor text-xs" valid={false}>
                        {imgErr ? imgErr : <ErrorMessage name="image" />}
                      </p>
                    </div>
                  </div>
                  <div className="mt-3">
                    <InputLabel
                      label={"Full Name"}
                      isMandatory={true}
                    ></InputLabel>
                    <TextFieldWrapper name="name" placeholder={"Enter name"} />
                  </div>
                  <div className="mt-3">
                    <InputLabel
                      label={" Email ID"}
                      isMandatory={true}
                    ></InputLabel>
                    <TextFieldWrapper
                      name="email"
                      placeholder={"Enter email"}
                      disabled={user || isSubmitting ? true : false}
                    />
                  </div>
                  <div className="mt-3">
                    <InputLabel
                      label={"Phone Number "}
                      isMandatory={true}
                    ></InputLabel>
                    {/* {console.log("CountryCodeArr", countryCodeArr)} */}
                    <div className="flex flex-row">
                      <Select
                        placeholder={""}
                        name="countryCode"
                        value={values.countryCode}
                        //className={"w-1/4"}
                        options={countryCodeArr.map((x) => {
                          return { label: x, value: x };
                        })}
                        isSearchable={true}
                      ></Select>
                      <div className="flex-grow ml-2">
                        <TextFieldWrapper
                          name="phoneNumber"
                          placeholder={"Enter phone number"}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="mt-3">
                    <InputLabel
                      label={"Assign Role"}
                      isMandatory={true}
                    ></InputLabel>
                    <Select
                      name="role"
                      value={values.role}
                      options={rolesState.map((x) => {
                        return { label: x.name, value: x.id };
                      })}
                      // onChange={() => handlerole(values)}
                      onChange={() => {
                        setFieldValue("typeList", "");
                      }}
                      isSearchable={true}
                    ></Select>
                  </div>
                  {hasKeyForId(values.role, "customerRole") ||
                  hasKeyForId(values.role, "supplierRole") ||
                  hasKeyForId(values.role, "customerSupplierRole") ? null : (
                    <div className="mt-3">
                      <InputLabel
                        label={"Default Dashboard"}
                        isMandatory={true}
                      ></InputLabel>
                      <Select
                        name="defaultDashboard"
                        value={values?.defaultDashboard}
                        options={dashboard?.map((x) => {
                          return { label: x.name, value: x.id };
                        })}
                        isSearchable={true}
                        //onBlur={handleBlur}
                      ></Select>
                    </div>
                  )}
                  <div className="flex flex-col mt-4">
                    {hasKeyForId(values.role, "customerRole") ||
                    hasKeyForId(values.role, "supplierRole") ||
                    hasKeyForId(values.role, "customerSupplierRole") ? null : (
                      <>
                        <InputLabel
                          label="Select any one option"
                          isMandatory={true}
                          labelClassName={"mb-2"}
                        />
                        <RadioGroup
                          row
                          id="typeList"
                          name="typeList"
                          onChange={handleChange}
                          onBlur={handleBlur}
                        >
                          <FormControlLabel
                            value="Customer"
                            control={
                              <Radio
                                sx={{
                                  "& .MuiSvgIcon-root": { fontSize: "16px" },
                                  "&.Mui-checked .MuiSvgIcon-root": {
                                    color: "black",
                                  },
                                }}
                              />
                            }
                            label={
                              <span className="text-tabColor text-sm">
                                Customer List
                              </span>
                            }
                            checked={values.typeList === "Customer"}
                          />
                          <div className="w-7" />
                          <FormControlLabel
                            value="Supplier"
                            control={
                              <Radio
                                sx={{
                                  "& .MuiSvgIcon-root": { fontSize: "16px" },
                                  "&.Mui-checked .MuiSvgIcon-root": {
                                    color: "black",
                                  },
                                }}
                              />
                            }
                            label={
                              <span className="text-tabColor text-sm">
                                Supplier List
                              </span>
                            }
                            checked={values.typeList === "Supplier"}
                          />
                          <div className="w-7" />
                          <FormControlLabel
                            value="Both"
                            control={
                              <Radio
                                sx={{
                                  "& .MuiSvgIcon-root": { fontSize: "16px" },
                                  "&.Mui-checked .MuiSvgIcon-root": {
                                    color: "black",
                                  },
                                }}
                              />
                            }
                            label={
                              <span className="text-tabColor text-sm">
                                Both
                              </span>
                            }
                            checked={values.typeList === "Both"}
                          />
                        </RadioGroup>
                      </>
                    )}

                    {errors.typeList && touched.typeList && (
                      <FormHelperText
                        style={{ top: "27px" }}
                        error
                        id="typeList"
                        className="text-errorColor"
                      >
                        {errors.typeList}
                      </FormHelperText>
                    )}
                    {values.typeList === "Customer" ||
                    values.typeList === "Both" ||
                    hasKeyForId(values.role, "customerRole") ||
                    (hasKeyForId(values.role, "customerSupplierRole") &&
                      !hasKeyForId(values.role, "supplierRole")) ? (
                      <div className="mt-3">
                        <InputLabel
                          label={"Customer List"}
                          isMandatory={true}
                        />
                        <CustomDropDown
                          btnWidth="w-full"
                          data={selectObjectsCustomer}
                          btnName={"Add Customers"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("customerList", selectedDetail);
                          }}
                          defaultSelectedData={customerData}
                          onBlur={() => setFieldTouched("customerList", true)}
                          isUser={true}
                        />
                        {errors.customerList && touched.customerList && (
                          <div className="text-errorColor text-xs">
                            {errors.customerList}
                          </div>
                        )}
                      </div>
                    ) : null}

                    {values.typeList === "Supplier" ||
                    values.typeList === "Both" ||
                    hasKeyForId(values.role, "supplierRole") ||
                    (hasKeyForId(values.role, "customerSupplierRole") &&
                      !hasKeyForId(values.role, "customerRole")) ? (
                      <div className="mt-3">
                        <InputLabel
                          label={"Supplier List"}
                          isMandatory={true}
                        />
                        <CustomDropDown
                          btnWidth="w-full"
                          data={selectObjectsSupplier}
                          btnName={"Add Suppliers"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("supplierList", selectedDetail);
                          }}
                          defaultSelectedData={supplierData}
                          onBlur={() => {
                            setFieldTouched("supplierList", true);
                          }}
                          isUser={true}
                        />

                        {errors.supplierList && touched.supplierList && (
                          <div className="text-errorColor text-xs">
                            {errors.supplierList}
                          </div>
                        )}
                      </div>
                    ) : null}
                  </div>
                  <div className="flex flex-col mt-4">
                    <>
                      <InputLabel
                        label="Type of user"
                        isMandatory={true}
                        labelClassName={"mb-2"}
                      />
                      <RadioGroup
                        row
                        id="isLdapUser"
                        name="isLdapUser"
                        onChange={handleChange}
                        onBlur={handleBlur}
                      >
                        <FormControlLabel
                          value="true"
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": { fontSize: "16px" },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-tabColor text-sm">
                              LDAP User
                            </span>
                          }
                          checked={values.isLdapUser === "true"}
                          disabled={!user ? false : true}
                        />
                        <div className="w-7" />
                        <FormControlLabel
                          value="false"
                          control={
                            <Radio
                              sx={{
                                "& .MuiSvgIcon-root": { fontSize: "16px" },
                                "&.Mui-checked .MuiSvgIcon-root": {
                                  color: "black",
                                },
                              }}
                            />
                          }
                          label={
                            <span className="text-tabColor text-sm">
                              Non LDAP User
                            </span>
                          }
                          checked={values.isLdapUser === "false"}
                          disabled={!user ? false : true}
                        />
                      </RadioGroup>
                    </>
                  </div>
                </div>
                <div style={{ textAlign: "center" }} className="mt-10 mb-10">
                  <BackButton
                    onClick={() => {
                      onHide();
                      resetForm();
                      setFile(null);
                      setSelectedImage(null);
                      setProfileData(null);
                    }}
                    label={"Cancel"}
                    buttonClassName={"md:w-[120px] w-full"}
                  ></BackButton>
                  <Button
                    type="submit"
                    disabled={!isValid || isSubmitting || imgErr}
                    buttonClassName={"md:w-[120px] w-full ml-5"}
                    label={"Save"}
                    loading={isSubmitting}
                  ></Button>
                </div>
              </Form>
            </Dialog>
          )}
        </Formik>
      </div>

      <ToastContainer position="top-center" autoClose={3000} />
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
    </div>
  );
}

export default CreateorEditModal;
