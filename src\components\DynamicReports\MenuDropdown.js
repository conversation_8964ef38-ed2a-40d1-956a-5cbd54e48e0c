import React from "react";
import { FilterIcon, MenusIcon, MailBoxIcon } from "../../icons";
import { CssTooltip } from "../StyledComponent";

const MenuDropdown = ({
  showMenuDropdown,
  setShowMenuDropdown,
  setOpenDialog,
  setSendMailDialog,
  setFilterDialog,
  badgeCount,
  labelData,
  menuRef,
}) => {
  return (
    <div className="relative" ref={menuRef}>
      <div
        className="rounded-full bg-bgouterBackground p-2 flex items-center justify-center cursor-pointer"
        onClick={() => {
          setShowMenuDropdown(!showMenuDropdown);
          setOpenDialog(false);
        }}
      >
        <CssTooltip title={"Report Menu"} placement="top" arrow>
          <MenusIcon className="w-5 h-5" />
        </CssTooltip>
      </div>

      {/* Dropdown Menu */}
      {showMenuDropdown && (
        <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200 p-3">
          <div className="grid grid-cols-2 gap-2">
            {/* Send Mail Option */}
            <div
              className="flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100"
              onClick={() => {
                setSendMailDialog(true);
                setShowMenuDropdown(false);
              }}
            >
              <MailBoxIcon className="w-5 h-5" />
              <span className="text-sm text-gray-700 ml-2">Send Email</span>
            </div>

            {/* Filters Option */}
            <div
              className={`flex items-center gap-1 cursor-pointer p-2 rounded hover:bg-gray-100`}
              onClick={() => {
                setFilterDialog(true);
                setShowMenuDropdown(false);
              }}
            >
              <div className="relative">
                <FilterIcon className="w-5 h-5" />
                {badgeCount > 0 && (
                  <CssTooltip
                    title={labelData.length > 0 ? labelData.join(", ") : ""}
                    placement="top"
                    arrow
                  >
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-4 w-4 flex items-center justify-center text-xs font-medium">
                      {badgeCount}
                    </div>
                  </CssTooltip>
                )}
              </div>
              <span className="text-sm text-gray-700 ml-2 truncate">
                Filters
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuDropdown;
