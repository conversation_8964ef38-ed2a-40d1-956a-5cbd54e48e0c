import React from "react";
import ExportPopup from "../../popups/exportpopup";
import SendMail from "../../popups/SendMail";
import ReportFilter from "../CollapsibleFilter/ReportFilter";
import { MetaDataProvider } from "../../context/MetaDataContext";

const Modals = ({
  // Filter dialog props
  filterDialog,
  setFilterDialog,
  setFilters,
  filters,
  setLabelData,
  panelById,

  // Export popup props
  showExportConfirmation,
  setShowExportConfirmation,
  exportReport,
  exportPermissions,

  // Send mail props
  sendMailDialog,
  setSendMailDialog,
  selectedFilter,
  extensionType,
  data,
  timeZone,
}) => {
  return (
    <>
      {/* Conditional Filter Dialog */}
      {filterDialog && (
        <MetaDataProvider>
          <ReportFilter
            openFilterDialog={filterDialog}
            closeFilterDialog={() => {
              setFilterDialog(false);
            }}
            reportName={"dynamicReports"}
            setFilters={setFilters}
            filterData={filters}
            setLabelData={setLabelData}
            visualizationType={panelById?.visualizationType}
          />
        </MetaDataProvider>
      )}

      {/* Export Popup for Table Reports */}
      {showExportConfirmation && (
        <ExportPopup
          show={showExportConfirmation}
          onHide={() => setShowExportConfirmation(false)}
          onConfirm={(type) => {
            exportReport(type);
            setShowExportConfirmation(false);
          }}
          title={"Export Report"}
          identity={"Reports"}
          exportPermissions={exportPermissions}
        />
      )}

      {/* Send Mail Dialog */}
      {sendMailDialog && (
        <SendMail
          openGroupDialog={sendMailDialog}
          closeGroupDialog={() => {
            setSendMailDialog(false);
          }}
          selectedFilter={selectedFilter}
          searchStr={""}
          type={extensionType}
          reportName={data}
          timeZone={timeZone}
        />
      )}
    </>
  );
};

export default Modals;
