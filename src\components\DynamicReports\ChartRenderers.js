import React from "react";
import Table from "../table/ReportTable";
import Doughnut<PERSON>hart from "../charts/DoughnutChart";
import MultiAxisChart from "../charts/MultiAxisChart";
import BarChartComponent from "../charts/BarCharts";
import LineChartComponent from "../charts/LineChart";
import Pagination from "../Pagination/Pagination";
import ResultPerPageComponent from "../Pagination/ResultsPerPage";
import { calculatePaginationInfo } from "../../utils/dynamicReports/tableUtils";

const ChartRenderers = ({
  panelById,
  responseData,
  colors,
  columns,
  refs,
  resultPerPage,
  limitPerPage,
  handleLimitChange,
  currentPage,
  handlePageChange,
}) => {
  const { pieChartRef, barChartRef, lineChartRef, tableRef, multiAxisRef } = refs;

  const renderVisualization = () => {
    switch (panelById.visualizationType) {
      case "Pie Chart":
        return (
          <div
            ref={pieChartRef}
            className="pie-chart-container"
            style={{
              display: "flex",
              width: "100%",
              height: "300px",
              margin: "auto",
            }}
          >
            <DoughnutChart
              className={"mx-auto"}
              respData={responseData?.data || []}
              reportData={true}
            />
          </div>
        );

      case "Bar Graph":
        return (
          <div
            ref={barChartRef}
            className="bar-chart-container"
            style={{
              display: "flex",
              width: "100%",
              height: "450px",
              margin: "auto",
            }}
          >
            <BarChartComponent
              className={"mx-auto"}
              chartData={responseData?.data || []}
            />
          </div>
        );

      case "Line Graph":
        return (
          <div
            ref={lineChartRef}
            className="line-chart-container"
            style={{
              display: "flex",
              width: "100%",
              height: "450px",
              margin: "auto",
            }}
          >
            <LineChartComponent
              data={responseData?.data}
              colors={colors}
            />
          </div>
        );

      case "Table Report":
        const paginationInfo = calculatePaginationInfo({
          currentPage,
          limitPerPage,
          totalCount: responseData?.totalCount,
        });

        return (
          <div ref={tableRef} className="table-container">
            <Table data={responseData?.data || []} columns={columns} />
            <div className="flex items-center justify-between mt-5">
              <div className="flex items-center">
                <ResultPerPageComponent
                  countPerPage={resultPerPage}
                  limit={limitPerPage}
                  handleLimitChange={handleLimitChange}
                  pageName="reports"
                />
                <div className="text-sm pl-3 text-titleColor">
                  {paginationInfo.displayText}
                </div>
              </div>

              <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={responseData?.totalCount}
                pageSize={limitPerPage}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        );

      case "MultiAxis Graph":
        return (
          <div
            ref={multiAxisRef}
            className="multi-axis-chart-container"
            style={{
              display: "flex",
              width: "100%",
              height: "450px",
              margin: "auto",
            }}
          >
            <MultiAxisChart data={responseData || []} />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`${
        panelById.visualizationType === "Table Report"
          ? "m-3"
          : "md:flex gap-5 border border-outerBorder m-5 p-3"
      }`}
    >
      {renderVisualization()}
    </div>
  );
};

export default ChartRenderers;
