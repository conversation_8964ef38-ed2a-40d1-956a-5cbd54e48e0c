import { useState, useEffect, useRef, useMemo, useCallback, useContext } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useMutation, useQuery } from "react-query";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

import { getId, previewPanel } from "../services/panels-api";
import { DataContext } from "../context/DataContext";
import { DownloadContext } from "../context/DownloadContext";
import { AuthContext } from "../context/AuthContext";
import { defaultTimeRange, timeAcessOptions } from "../common/constants";

import {
  buildPreviewRequestData,
  handleAPISuccess,
  handleAPIError,
} from "../utils/dynamicReports/apiUtils";
import {
  calculateExportPermissions,
  generateTooltipContent,
  handleExportProcess,
} from "../utils/dynamicReports/exportUtils";
import {
  handleChartPDFDownload,
  getTargetElementForPDF,
} from "../utils/dynamicReports/pdfUtils";
import { generateTableColumns } from "../utils/dynamicReports/tableUtils";

dayjs.extend(customParseFormat);

export const useDynamicReports = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { value: data, viewBy, activeTab, subtype, id } = location.state || {};

  // State management
  const [filters, setFilters] = useState([]);
  const [labelData, setLabelData] = useState([]);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRange, setSelectedRange] = useState("Today");
  const [filterDialog, setFilterDialog] = useState(false);
  const [panelById, setPanelById] = useState("");
  const [responseData, setResponseData] = useState([]);
  const [date, setDate] = useState({});
  const [errorMessage, setErrorMessage] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [sendMailDialog, setSendMailDialog] = useState(false);
  const [extensionType, setExtensionType] = useState("");
  const [selectedFilter, setSelectedFilter] = useState(() => {
    const startDate = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
    const endDate = dayjs().format("YYYY-MM-DD HH:mm:ss");
    return { startDate, endDate };
  });

  // Refs
  const menuRef = useRef(null);
  const pieChartRef = useRef(null);
  const barChartRef = useRef(null);
  const lineChartRef = useRef(null);
  const tableRef = useRef(null);
  const multiAxisRef = useRef(null);

  // Context
  const { resultPerPage } = useContext(DataContext);
  const { isDownloading, setIsDownloading } = useContext(DownloadContext);
  const { configApiData } = useContext(AuthContext);

  // Computed values
  const getUrl = new URL(window.location.href);
  const timeZone = getUrl.search.slice(1);
  const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];

  const exportPermissions = useMemo(
    () => calculateExportPermissions(responseData?.count || 0, configApiData),
    [responseData?.count, configApiData]
  );

  const tooltipContent = useMemo(
    () => generateTooltipContent(configApiData),
    [configApiData]
  );

  const filteredKeys = Object.keys(filters).filter(
    (key) => key !== "durationTime"
  );
  const badgeCount = filteredKeys.length;

  // API mutations and queries
  const { mutate: previewPanelAPIData, isLoading: loadingData } =
    useMutation(previewPanel);

  useQuery(["getPanelById", id], getId, {
    enabled: !!id,
    onSuccess: ({ data }) => {
      setPanelById(data);
      setSelectedRange(data?.timePeriod);
      previewById(data);
    },
    refetchOnWindowFocus: false,
  });

  // Utility functions
  const getTimeRange = (viewBy) => {
    if (viewBy.length === 5) {
      return defaultTimeRange;
    } else {
      let newRange = viewBy.map((x) => {
        return timeAcessOptions[x];
      });
      return [...new Set(newRange.flat(1))];
    }
  };

  // Event handlers
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleLimitChange = (e) => {
    setCurrentPage(1);
    setLimitPerPage(e?.target?.value);
  };

  // Main API call function
  const previewById = useCallback(
    (data, reportFilters = {}, isDownload = false) => {
      const reqData = buildPreviewRequestData({
        data,
        reportFilters,
        isDownload,
        timeZone,
        limitPerPage,
        currentPage,
      });

      previewPanelAPIData(
        { reqData },
        {
          onSuccess: ({ data: responseData }) => {
            const { formattedStart, formattedEnd } = buildPreviewRequestData({
              data,
              reportFilters,
              isDownload,
              timeZone,
              limitPerPage,
              currentPage,
            });
            handleAPISuccess({
              data: responseData,
              setErrorMessage,
              setResponseData,
              setDate,
              formattedStart,
              formattedEnd,
            });
          },
          onError: (error) => {
            handleAPIError({ error, setErrorMessage });
          },
        }
      );
    },
    [previewPanelAPIData, limitPerPage, currentPage, timeZone]
  );

  // Export function
  const exportReport = useCallback(
    (type) => {
      handleExportProcess({
        panelById,
        filters,
        timeZone,
        limitPerPage,
        currentPage,
        type,
        data,
        date,
        responseData,
        configApiData,
        setIsDownloading,
      });
    },
    [
      panelById,
      filters,
      timeZone,
      limitPerPage,
      currentPage,
      data,
      date,
      responseData,
      configApiData,
      setIsDownloading,
    ]
  );

  // Download handler
  const handleDownloadPDF = useCallback(() => {
    const visualizationType = panelById?.visualizationType;

    if (visualizationType === "Table Report") {
      setShowExportConfirmation(true);
      return;
    }

    const refs = { pieChartRef, barChartRef, lineChartRef, multiAxisRef };
    const targetElement = getTargetElementForPDF(visualizationType, refs);

    if (targetElement) {
      handleChartPDFDownload({
        visualizationType,
        targetElement,
        data,
      });
    }
  }, [panelById?.visualizationType, data]);

  // Table columns
  const columns = useMemo(
    () => generateTableColumns(responseData, panelById?.visualizationType),
    [responseData?.data, panelById?.visualizationType]
  );

  return {
    // State
    filters,
    setFilters,
    labelData,
    setLabelData,
    limitPerPage,
    currentPage,
    selectedRange,
    setSelectedRange,
    filterDialog,
    setFilterDialog,
    panelById,
    responseData,
    date,
    errorMessage,
    showMenuDropdown,
    setShowMenuDropdown,
    openDialog,
    setOpenDialog,
    showExportConfirmation,
    setShowExportConfirmation,
    sendMailDialog,
    setSendMailDialog,
    extensionType,
    selectedFilter,
    setSelectedFilter,
    loadingData,

    // Refs
    menuRef,
    pieChartRef,
    barChartRef,
    lineChartRef,
    tableRef,
    multiAxisRef,

    // Context values
    resultPerPage,
    isDownloading,
    configApiData,

    // Computed values
    timeZone,
    colors,
    exportPermissions,
    tooltipContent,
    badgeCount,
    columns,

    // Functions
    getTimeRange,
    handlePageChange,
    handleLimitChange,
    previewById,
    exportReport,
    handleDownloadPDF,

    // Navigation data
    data,
    viewBy,
    activeTab,
    subtype,
    navigate,
  };
};
