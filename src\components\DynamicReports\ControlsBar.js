import React from "react";
import { InfoIcon } from "../../icons";
import { CssTooltip } from "../StyledComponent";
import Button from "../Button/OutlinedButton";
import ReportCalendar from "../DatePicker/ReportCalendar";
import MenuDropdown from "./MenuDropdown";

const ControlsBar = ({
  // Menu dropdown props
  showMenuDropdown,
  setShowMenuDropdown,
  setOpenDialog,
  setSendMailDialog,
  setFilterDialog,
  badgeCount,
  labelData,
  menuRef,
  
  // Calendar props
  selectedFilter,
  setSelectedFilter,
  setSelectedRange,
  selectedRange,
  getTimeRange,
  viewBy,
  subtype,
  openDialog,
  data,
  filters,
  
  // Download props
  panelById,
  tooltipContent,
  handleDownloadPDF,
  loadingData,
  isDownloading,
}) => {
  return (
    <div className="mx-3 flex flex-wrap items-center justify-end gap-y-3">
      {/* Calendar, Info Tooltip, Mail, Filter, Refresh, Download */}
      <div className="flex items-center space-x-8">
        {/* Menu Icon with Dropdown */}
        <MenuDropdown
          showMenuDropdown={showMenuDropdown}
          setShowMenuDropdown={setShowMenuDropdown}
          setOpenDialog={setOpenDialog}
          setSendMailDialog={setSendMailDialog}
          setFilterDialog={setFilterDialog}
          badgeCount={badgeCount}
          labelData={labelData}
          menuRef={menuRef}
        />

        {/* Info Tooltip */}
        <CssTooltip
          title={
            <div className="text-xs p-1">
              <p className="mb-1.5">
                During calendar selection, to view reports spanning more than an
                hour, a day, a week, or a month, make sure to set the start time
                to 00:00.
              </p>
              <p className="mb-1.5">
                In case of selection of last 6, 12, 24 hours; reports will be
                generated on an hourly basis with the start time of the selected
                hour.
              </p>
              <p className="mb-1.5">
                For example, if the current user time range is from 13:01 to
                13:59, then the report will be generated from 13:00 hour
                onwards.
              </p>
            </div>
          }
          placement="top"
          arrow
        >
          <InfoIcon className="ml-2 mt-1 w-4 h-3.5" />
        </CssTooltip>

        {/* Calendar Picker */}
        <div
          className={`${
            filters?.duration === "weekly" || filters?.duration === "monthly"
              ? "pointer-events-none opacity-50"
              : ""
          }`}
          onClick={() => {
            setShowMenuDropdown(false);
          }}
        >
          <ReportCalendar
            selectedFilter={selectedFilter}
            setSelectedFilter={setSelectedFilter}
            setSelectedRange={setSelectedRange}
            selectedRange={selectedRange}
            reportTimeRange={getTimeRange(viewBy)}
            viewBy={viewBy}
            subtype={subtype}
            openDialog={openDialog}
            setOpenDialog={setOpenDialog}
            data={data}
            isAdmin={false}
          />
        </div>

        {/* Download Button */}
        {panelById?.visualizationType === "Table Report" ? (
          <CssTooltip
            title={tooltipContent}
            placement="top"
            arrow
            PopperProps={{
              modifiers: [
                {
                  name: "preventOverflow",
                  options: {
                    altBoundary: true,
                  },
                },
              ],
            }}
            enterNextDelay={100}
            enterDelay={100}
            leaveDelay={200}
            componentsProps={{
              popper: {
                sx: {
                  opacity: 1,
                },
              },
            }}
          >
            <span style={{ display: "inline-block" }}>
              <Button
                buttonClassName="text-xs w-32 text-white h-10 rounded-md"
                label="Download"
                onClick={handleDownloadPDF}
                disabled={loadingData || isDownloading}
              />
            </span>
          </CssTooltip>
        ) : (
          <Button
            buttonClassName="text-xs w-32 text-white h-10 rounded-md"
            label="Download"
            onClick={handleDownloadPDF}
            disabled={loadingData}
          />
        )}
      </div>
    </div>
  );
};

export default ControlsBar;
